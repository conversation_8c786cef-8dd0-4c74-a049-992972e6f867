# Ptrade版本 - 集合竞价三合一策略
# 2024/08/01  止损卖出修改为跌破5日均线

import pandas as pd
import numpy as np
import datetime as dt
from datetime import datetime
from datetime import timedelta
import math


def initialize(context):
    # 一进二
    run_daily(context, get_stock_list, '9:01')
    run_daily(context, buy, '09:31')
    run_daily(context, sell, '11:25')
    run_daily(context, sell, '14:50')


# 选股
def get_stock_list(context):
    # 文本日期
    date = get_trading_day(-1)
    date_1 = get_trading_day(-2)
    date_2 = get_trading_day(-3)

    # 初始列表
    initial_list = prepare_stock_list(date)
    # 昨日涨停
    hl_list = get_hl_stock(initial_list, date)
    # 前日曾涨停
    hl1_list = get_ever_hl_stock(initial_list, date_1)
    # 前前日曾涨停
    hl2_list = get_ever_hl_stock(initial_list, date_2)
    # 合并 hl1_list 和 hl2_list 为一个集合，用于快速查找需要剔除的元素
    elements_to_remove = set(hl1_list + hl2_list)
    # 使用列表推导式来剔除 hl_list 中存在于 elements_to_remove 集合中的元素
    hl_list = [stock for stock in hl_list if stock not in elements_to_remove]

    g.target_list = hl_list

    # 昨日曾涨停
    h1_list = get_ever_hl_stock2(initial_list, date)
    # 上上个交易日涨停过滤
    elements_to_remove = get_hl_stock(initial_list, date_1)

    # 过滤上上个交易日涨停、曾涨停
    all_list = [stock for stock in h1_list if stock not in elements_to_remove]

    g.target_list2 = all_list


# 交易
def buy(context):
    # 先进行择时判断
    log.info('开始进行择时判断...')
    timing_result = select_timing(context)
    log.info('择时判断结果:' + str(timing_result))

    if not timing_result:
        log.info('今日择时信号不满足，不进行交易')
        return

    log.info('择时信号满足，开始选股...')
    qualified_stocks = []
    gk_stocks=[]
    dk_stocks=[]
    rzq_stocks=[]
    
    current_date = get_trading_day(0)
    # 确保日期是字符串格式
    if hasattr(current_date, 'strftime'):
        current_date_str = current_date.strftime('%Y-%m-%d')
    else:
        current_date_str = str(current_date)

    mid_time1 = ' 09:15:00'
    end_times1 = ' 09:26:00'
    start = current_date_str + mid_time1
    end = current_date_str + end_times1
    
    # 高开
    for s in g.target_list:
        # 条件一：均价，金额，市值，换手率
        prev_day_data = get_history(1, '1d', ['close', 'volume', 'money'], s, include=False)
        if prev_day_data.empty:
            continue
        avg_price_increase_value = prev_day_data['money'].iloc[-1] / prev_day_data['volume'].iloc[-1] / prev_day_data['close'].iloc[-1] * 1.1 - 1
        if avg_price_increase_value < 0.07 or prev_day_data['money'].iloc[-1] < 5.5e8 or prev_day_data['money'].iloc[-1] > 20e8 :
            continue
        
        # 获取基本面数据 - Ptrade API用法
        try:
            trading_day = get_trading_day(-1)
            # 确保日期是字符串格式
            if hasattr(trading_day, 'strftime'):
                date_str = trading_day.strftime('%Y-%m-%d')
            else:
                date_str = str(trading_day)

            fundamentals_data = get_fundamentals(
                table='valuation',
                columns=['code', 'market_cap', 'circulating_market_cap'],
                code=[s],
                date=date_str
            )
            if fundamentals_data.empty:
                continue
            market_cap = fundamentals_data['market_cap'].iloc[0]
            circulating_market_cap = fundamentals_data['circulating_market_cap'].iloc[0]
            if market_cap < 70 or circulating_market_cap > 520:
                continue
        except:
            # 如果获取基本面数据失败，跳过该股票
            continue

        # 条件二：左压
        zyts = calculate_zyts(s, context)
        volume_data = get_history(zyts, '1d', ['volume'], s, include=False)
        if len(volume_data) < 2 or volume_data['volume'].iloc[-1] <= volume_data['volume'].iloc[:-1].max() * 0.9:
            continue

        # 条件三：高开,开比
        # 获取集合竞价数据
        trend_data = get_trend_data(s, start, end)
        if trend_data.empty:
            continue
        auction_volume = trend_data['volume'].iloc[0] if len(trend_data) > 0 else 0
        auction_price = trend_data['close'].iloc[0] if len(trend_data) > 0 else 0
        
        if auction_volume / volume_data['volume'].iloc[-1] < 0.03:
            continue
        
        # 获取涨停价
        snapshot = get_snapshot(s)
        high_limit = snapshot[s]['up_px']
        current_ratio = auction_price / (high_limit/1.1)
        if current_ratio<=1 or current_ratio>=1.06:
            continue

        # 如果股票满足所有条件，则添加到列表中
        gk_stocks.append(s)
        qualified_stocks.append(s)


    # 低开
    # 基础信息
    date = get_trading_day(-1)

    # 昨日涨停列表
    initial_list = prepare_stock_list2(date)
    hl_list = get_hl_stock(initial_list, date)

    if len(hl_list) != 0:
        # 获取非连板涨停的股票
        ccd = get_continue_count_df(hl_list, date, 10)
        lb_list = list(ccd.index) if not ccd.empty else []
        stock_list = [s for s in hl_list if s not in lb_list]

        # 计算相对位置
        rpd = get_relative_position_df(stock_list, date, 60)
        if not rpd.empty:
            rpd = rpd[rpd['rp'] <= 0.5]
            stock_list = list(rpd.index)

        # 低开
        if len(stock_list) > 0:
            # Ptrade中需要逐个获取股票数据
            price_dict = {}
            for s in stock_list:
                try:
                    # 确保日期是字符串格式
                    if hasattr(date, 'strftime'):
                        date_str = date.strftime('%Y-%m-%d')
                    else:
                        date_str = str(date)

                    df = get_price(s, end_date=date_str, frequency='1d', fields=['close'], count=1)
                    if not df.empty:
                        price_dict[s] = df['close'].iloc[0]
                except:
                    continue

            if price_dict:
                snapshot_data = get_snapshot(list(price_dict.keys()))
                filtered_stocks = []
                for s in price_dict.keys():
                    if s in snapshot_data:
                        day_open = snapshot_data[s]['open_px']
                        close_price = price_dict[s]
                        if close_price > 0:
                            open_pct = day_open / close_price
                            if 0.955 <= open_pct <= 0.97:
                                filtered_stocks.append(s)
                stock_list = filtered_stocks

                for s in stock_list:
                    prev_day_data = get_history(1, '1d', ['close', 'volume', 'money'], s, include=False)
                    if not prev_day_data.empty and prev_day_data['money'].iloc[-1] >= 1e8:
                        dk_stocks.append(s)
                        qualified_stocks.append(s)

    # 弱转强
    for s in g.target_list2:
        # 过滤前面三天涨幅超过28%的票
        price_data = get_history(4, '1d', ['close'], s, include=False)
        if len(price_data) < 4:
            continue
        increase_ratio = (price_data['close'].iloc[-1] - price_data['close'].iloc[0]) / price_data['close'].iloc[0]
        if increase_ratio > 0.28:
            continue

        # 过滤前一日收盘价小于开盘价5%以上的票
        prev_day_data = get_history(1, '1d', ['open', 'close'], s, include=False)
        if len(prev_day_data) < 1:
            continue
        open_close_ratio = (prev_day_data['close'].iloc[0] - prev_day_data['open'].iloc[0]) / prev_day_data['open'].iloc[0]
        if open_close_ratio < -0.05:
            continue

        prev_day_data = get_history(1, '1d', ['close', 'volume','money'], s, include=False)
        if prev_day_data.empty:
            continue
        avg_price_increase_value = prev_day_data['money'].iloc[0] / prev_day_data['volume'].iloc[0] / prev_day_data['close'].iloc[0] - 1
        if avg_price_increase_value < -0.04 or prev_day_data['money'].iloc[0] < 3e8 or prev_day_data['money'].iloc[0] > 19e8:
            continue
        
        # 获取基本面数据 - Ptrade API用法
        try:
            trading_day = get_trading_day(-1)
            # 确保日期是字符串格式
            if hasattr(trading_day, 'strftime'):
                date_str = trading_day.strftime('%Y-%m-%d')
            else:
                date_str = str(trading_day)

            fundamentals_data = get_fundamentals(
                table='valuation',
                columns=['code', 'market_cap', 'circulating_market_cap'],
                code=[s],
                date=date_str
            )
            if fundamentals_data.empty:
                continue
            market_cap = fundamentals_data['market_cap'].iloc[0]
            circulating_market_cap = fundamentals_data['circulating_market_cap'].iloc[0]
            if market_cap < 70 or circulating_market_cap > 520:
                continue
        except:
            # 如果获取基本面数据失败，跳过该股票
            continue

        zyts = calculate_zyts(s, context)
        volume_data = get_history(zyts, '1d', ['volume'], s, include=False)
        if len(volume_data) < 2 or volume_data['volume'].iloc[-1] <= volume_data['volume'].iloc[:-1].max() * 0.9:
            continue

        # 获取集合竞价数据
        trend_data = get_trend_data(s, start, end)
        if trend_data.empty:
            continue
        auction_volume = trend_data['volume'].iloc[0] if len(trend_data) > 0 else 0
        auction_price = trend_data['close'].iloc[0] if len(trend_data) > 0 else 0

        if auction_volume / volume_data['volume'].iloc[-1] < 0.03:
            continue
        
        # 获取涨停价
        snapshot = get_snapshot(s)
        high_limit = snapshot[s]['up_px']
        current_ratio = auction_price / (high_limit/1.1)
        if current_ratio <= 0.98 or current_ratio >= 1.09:
            continue
        rzq_stocks.append(s)
        qualified_stocks.append(s)


    if len(qualified_stocks)>0:
        log.info('———————————————————————————————————')
        log.info('今日选股：'+','.join(qualified_stocks))
        log.info('一进二：'+','.join(gk_stocks))
        log.info('首板低开：'+','.join(dk_stocks))
        log.info('弱转强：'+','.join(rzq_stocks))
        log.info('今日选股：'+','.join(qualified_stocks))
        log.info('———————————————————————————————————')
    else:
        log.info('今日无目标个股')


    if len(qualified_stocks)!=0 and context.portfolio.cash/context.portfolio.total_value>0.3:
        value = context.portfolio.cash / len(qualified_stocks)
        for s in qualified_stocks:
            # 下单
            snapshot = get_snapshot(s)
            last_price = snapshot[s]['last_px']
            day_open = snapshot[s]['open_px']
            #由于关闭了错误日志，不加这一句，不足一手买入失败也会打印买入，造成日志不准确
            if context.portfolio.cash/last_price>100:
                order_value(s, value, limit_price=day_open)
                log.info('买入' + s)
                log.info('———————————————————————————————————')

# 处理日期相关函数
def transform_date(date, date_type):
    if type(date) == str:
        str_date = date
        # 处理不同的日期格式
        if '-' in date:
            dt_date = dt.datetime.strptime(date, '%Y-%m-%d')
        else:
            # 处理YYYYmmdd格式
            dt_date = dt.datetime.strptime(date, '%Y%m%d')
        d_date = dt_date.date()
    elif type(date) == dt.datetime:
        str_date = date.strftime('%Y-%m-%d')
        dt_date = date
        d_date = dt_date.date()
    elif type(date) == dt.date:
        str_date = date.strftime('%Y-%m-%d')
        dt_date = dt.datetime.strptime(str_date, '%Y-%m-%d')
        d_date = date
    dct = {'str':str_date, 'dt':dt_date, 'd':d_date}
    return dct[date_type]

def get_shifted_date(date, days, days_type='T'):
    #获取上一个自然日
    d_date = transform_date(date, 'd')
    yesterday = d_date + dt.timedelta(-1)
    #移动days个自然日
    if days_type == 'N':
        shifted_date = yesterday + dt.timedelta(days+1)
    #移动days个交易日
    if days_type == 'T':
        all_trade_days = get_all_trades_days()
        all_trade_days = [i.strftime('%Y-%m-%d') for i in all_trade_days]
        #如果上一个自然日是交易日，根据其在交易日列表中的index计算平移后的交易日
        if str(yesterday) in all_trade_days:
            shifted_date = all_trade_days[all_trade_days.index(str(yesterday)) + days + 1]
        #否则，从上一个自然日向前数，先找到最近一个交易日，再开始平移
        else:
            for i in range(100):
                last_trade_date = yesterday - dt.timedelta(i)
                if str(last_trade_date) in all_trade_days:
                    shifted_date = all_trade_days[all_trade_days.index(str(last_trade_date)) + days + 1]
                    break
    return str(shifted_date)


# 过滤函数
def filter_new_stock(initial_list, date, days=50):
    d_date = transform_date(date, 'd')
    filtered_list = []
    for stock in initial_list:
        stock_info = get_stock_info(stock)
        if stock_info and 'start_date' in stock_info:
            start_date = dt.datetime.strptime(stock_info['start_date'], '%Y-%m-%d').date()
            if d_date - start_date > dt.timedelta(days=days):
                filtered_list.append(stock)
    return filtered_list

def filter_st_stock(initial_list, date):
    # Ptrade中使用get_stock_status来过滤ST股票
    filtered_list = []
    for stock in initial_list:
        try:
            status_info = get_stock_status(stock, query_type='ST')
            if not status_info or len(status_info) == 0:
                filtered_list.append(stock)
        except:
            filtered_list.append(stock)
    return filtered_list

def filter_kcbj_stock(initial_list):
    return [stock for stock in initial_list
    if not stock.startswith('4')
    and not stock.startswith('8')
    # and not stock.startswith('3')
    and not stock.startswith('68')]

def filter_paused_stock(initial_list, date):
    # Ptrade中使用get_stock_status来过滤停牌股票
    filtered_list = []
    for stock in initial_list:
        try:
            status_info = get_stock_status(stock, query_type='HALT')
            if not status_info or len(status_info) == 0:
                filtered_list.append(stock)
        except:
            filtered_list.append(stock)
    return filtered_list

# 一字
def filter_extreme_limit_stock(context, stock_list, date):
    tmp = []
    for stock in stock_list:
        try:
            # 确保日期是字符串格式
            if hasattr(date, 'strftime'):
                date_str = date.strftime('%Y-%m-%d')
            else:
                date_str = str(date)

            df = get_price(stock, end_date=date_str, frequency='1d', fields=['low','high_limit'], count=1)
            if not df.empty and df.iloc[0]['low'] < df.iloc[0]['high_limit']:
                tmp.append(stock)
        except:
            continue
    return tmp


# 每日初始股票池
def prepare_stock_list(date):
    # 确保date是字符串格式，get_Ashares需要YYYYmmdd格式
    if hasattr(date, 'strftime'):
        date_str = date.strftime('%Y%m%d')
    else:
        # 如果是字符串，尝试转换格式
        if isinstance(date, str) and '-' in date:
            date_str = date.replace('-', '')
        else:
            date_str = str(date)

    initial_list = get_Ashares(date_str)
    initial_list = filter_kcbj_stock(initial_list)
    # 过滤函数需要原始的date对象，不是date_str
    initial_list = filter_new_stock(initial_list, date)
    initial_list = filter_st_stock(initial_list, date)
    initial_list = filter_paused_stock(initial_list, date)
    return initial_list


# 计算左压天数
def calculate_zyts(s, context):
    high_prices = get_history(101, '1d', ['high'], s, include=False)
    if high_prices.empty or len(high_prices) < 2:
        return 100
    prev_high = high_prices['high'].iloc[-1]
    zyts_0 = 100
    for i in range(2, min(len(high_prices), 100)):
        if high_prices['high'].iloc[-(i+1)] >= prev_high:
            zyts_0 = i-1
            break
    zyts = zyts_0 + 5
    return zyts


# 筛选出某一日涨停的股票
def get_hl_stock(initial_list, date):
    hl_list = []
    for stock in initial_list:
        try:
            # 确保日期是字符串格式
            if hasattr(date, 'strftime'):
                date_str = date.strftime('%Y-%m-%d')
            else:
                date_str = str(date)

            df = get_price(stock, end_date=date_str, frequency='1d', fields=['close','high_limit'], count=1)
            if not df.empty and abs(df.iloc[0]['close'] - df.iloc[0]['high_limit']) < 0.01:
                hl_list.append(stock)
        except:
            continue
    return hl_list

# 筛选曾涨停
def get_ever_hl_stock(initial_list, date):
    hl_list = []
    for stock in initial_list:
        try:
            # 确保日期是字符串格式
            if hasattr(date, 'strftime'):
                date_str = date.strftime('%Y-%m-%d')
            else:
                date_str = str(date)

            df = get_price(stock, end_date=date_str, frequency='1d', fields=['high','high_limit'], count=1)
            if not df.empty and abs(df.iloc[0]['high'] - df.iloc[0]['high_limit']) < 0.01:
                hl_list.append(stock)
        except:
            continue
    return hl_list

# 筛选曾涨停
def get_ever_hl_stock2(initial_list, date):
    hl_list = []
    for stock in initial_list:
        try:
            # 确保日期是字符串格式
            if hasattr(date, 'strftime'):
                date_str = date.strftime('%Y-%m-%d')
            else:
                date_str = str(date)

            df = get_price(stock, end_date=date_str, frequency='1d', fields=['close','high','high_limit'], count=1)
            if not df.empty:
                cd1 = abs(df.iloc[0]['high'] - df.iloc[0]['high_limit']) < 0.01
                cd2 = abs(df.iloc[0]['close'] - df.iloc[0]['high_limit']) >= 0.01
                if cd1 and cd2:
                    hl_list.append(stock)
        except:
            continue
    return hl_list

# 计算涨停数
def get_hl_count_df(hl_list, date, watch_days):
    # 获取watch_days的数据
    hl_count_list = []
    extreme_hl_count_list = []
    for stock in hl_list:
        try:
            # 确保日期是字符串格式
            if hasattr(date, 'strftime'):
                date_str = date.strftime('%Y-%m-%d')
            else:
                date_str = str(date)

            df = get_price(stock, end_date=date_str, frequency='1d', fields=['close','high_limit','low'], count=watch_days)
            if not df.empty:
                hl_days = sum(abs(df['close'] - df['high_limit']) < 0.01)
                extreme_hl_days = sum(abs(df['low'] - df['high_limit']) < 0.01)
                hl_count_list.append(hl_days)
                extreme_hl_count_list.append(extreme_hl_days)
            else:
                hl_count_list.append(0)
                extreme_hl_count_list.append(0)
        except:
            hl_count_list.append(0)
            extreme_hl_count_list.append(0)
    #创建df记录
    df = pd.DataFrame(index=hl_list, data={'count':hl_count_list, 'extreme_count':extreme_hl_count_list})
    return df

# 计算连板数
def get_continue_count_df(hl_list, date, watch_days):
    df = pd.DataFrame()
    for d in range(2, watch_days+1):
        HLC = get_hl_count_df(hl_list, date, d)
        CHLC = HLC[HLC['count'] == d]
        df = pd.concat([df, CHLC])
    stock_list = list(set(df.index))
    ccd = pd.DataFrame()
    for s in stock_list:
        tmp = df.loc[[s]]
        if len(tmp) > 1:
            M = tmp['count'].max()
            tmp = tmp[tmp['count'] == M]
        ccd = pd.concat([ccd, tmp])
    if len(ccd) != 0:
        ccd = ccd.sort_values(by='count', ascending=False)
    return ccd

# 计算昨涨幅
def get_index_increase_ratio(index_code, context):
    # 获取指数昨天和前天的收盘价
    close_prices = get_history(2, '1d', ['close'], index_code, include=False)
    if len(close_prices) < 2:
        return 0  # 如果数据不足，返回0
    day_before_yesterday_close = close_prices['close'].iloc[0]
    yesterday_close = close_prices['close'].iloc[1]

    # 计算涨幅
    increase_ratio = (yesterday_close - day_before_yesterday_close) / day_before_yesterday_close
    return increase_ratio

# 获取股票所属行业
def getStockIndustry(stocks):
    try:
        # 使用get_stock_blocks函数获取股票的行业信息
        industry_dict = {}
        for stock in stocks:
            blocks = get_stock_blocks(stock)
            if blocks and len(blocks) > 0:
                # 取第一个行业作为主要行业
                industry_dict[stock] = blocks[0]['block_name'] if 'block_name' in blocks[0] else ''
            else:
                industry_dict[stock] = ''
        return pd.Series(industry_dict)
    except Exception as e:
        log.info(f"获取行业信息出错: {str(e)}")
        return pd.Series()

# 获取市场宽度
def get_market_breadth(context):
    try:
        log.info("开始计算市场宽度...")
        # 指定日期防止未来数据
        yesterday = get_trading_day(-1)
        log.info(f"使用日期: {yesterday}")

        # 获取初始列表 - 使用中证全指
        stocks = get_index_stocks("000985.SS")
        log.info(f"获取到 {len(stocks)} 只股票")

        # 获取股票价格数据
        count = 1
        # 确保yesterday是字符串格式
        if hasattr(yesterday, 'strftime'):
            yesterday_str = yesterday.strftime('%Y-%m-%d')
        else:
            yesterday_str = str(yesterday)

        # Ptrade中get_price需要逐个获取股票数据，增加数量以获得更多有效数据
        price_data_list = []
        success_count = 0
        for stock in stocks[:500]:  # 增加处理数量
            try:
                df = get_price(
                    stock,
                    end_date=yesterday_str,
                    frequency="1d",
                    fields=["close"],
                    count=count + 20
                )
                if not df.empty and len(df) >= 20:  # 确保有足够的历史数据
                    df['code'] = stock
                    price_data_list.append(df)
                    success_count += 1
                    if success_count >= 200:  # 获得足够数量的有效数据后停止
                        break
            except Exception as e:
                continue

        log.info(f"成功获取 {len(price_data_list)} 只股票的价格数据")

        if not price_data_list:
            log.info("获取价格数据失败")
            return ""

        h = pd.concat(price_data_list, ignore_index=True)

        # 处理数据
        try:
            # 为每行数据添加日期信息
            h["date"] = pd.to_datetime(h.index).date if 'time' not in h.columns else pd.to_datetime(h['time']).dt.date

            # 去除重复的code-date组合，保留最后一个
            h = h.drop_duplicates(subset=['code', 'date'], keep='last')

            df_close = h.pivot(index="code", columns="date", values="close").dropna(axis=0)
            log.info(f"有效数据股票数量: {len(df_close)}")

            if df_close.empty or df_close.shape[1] < 20:
                log.info("数据不足，无法计算20日均线")
                return ""

            # 计算20日均线
            df_ma20 = df_close.rolling(window=20, axis=1).mean().iloc[:, -count:]

            # 计算偏离程度（股价是否高于20日均线）
            df_bias = df_close.iloc[:, -count:] > df_ma20
        except Exception as e:
            log.info(f"数据处理失败: {str(e)}")
            return ""

        # 获取股票所属行业
        industry_series = getStockIndustry(stocks)
        log.info(f"获取到 {len(industry_series)} 只股票的行业信息")

        # 将行业信息添加到df_bias
        df_bias["industry_name"] = industry_series

        # 去除没有行业信息的股票
        df_bias = df_bias.dropna(subset=["industry_name"])
        log.info(f"有效行业信息股票数量: {len(df_bias)}")

        # 计算行业偏离比例
        df_ratio = ((df_bias.groupby("industry_name").sum() * 100.0) / df_bias.groupby("industry_name").count()).round()
        log.info(f"行业数量: {len(df_ratio)}")

        # 获取偏离程度最高的行业（仅获取Top1）
        # 处理yesterday的日期格式
        if isinstance(yesterday, str):
            if '-' in yesterday:
                yesterday_date = dt.datetime.strptime(yesterday, '%Y-%m-%d').date()
            else:
                yesterday_date = dt.datetime.strptime(yesterday, '%Y%m%d').date()
        else:
            yesterday_date = yesterday

        if yesterday_date in df_ratio.columns:
            top_value = df_ratio.loc[:, yesterday_date].nlargest(1)
            top_industry = top_value.index.tolist()[0] if len(top_value) > 0 else ""
        else:
            top_industry = ""
        log.info("市场宽度计算结果 - 领先行业Top1:" + str(top_industry))
        return top_industry
    except Exception as e:
        log.info(f"市场宽度计算失败: {str(e)}")
        # 出错时返回空字符串
        return ""

# 择时判断
def select_timing(context):
    log.info("开始执行择时判断函数...")
    try:
        # 获取市场宽度Top1行业
        top_industry = get_market_breadth(context)
        log.info("获取到的市场宽度领先行业Top1:" + str(top_industry))

        # 需要监控的行业
        restricted_industries = ["银行", "有色金属", "钢铁", "煤炭"]
        log.info("需要监控的行业:" + str(restricted_industries))

        # 检查Top1行业是否在需要监控的行业中
        is_restricted = False
        for industry in restricted_industries:
            if industry in top_industry:
                is_restricted = True
                log.info(f"Top1行业 '{top_industry}' 包含监控行业 '{industry}'")
                break

        if not is_restricted:
            log.info("Top1行业不在监控行业中，择时条件满足，可以交易")
            return True
        else:
            log.info("Top1行业在监控行业中，择时条件不满足，不进行交易")
            return False
    except Exception as e:
        log.info("择时判断出错:" + str(e))
        # 出错时默认允许交易
        return True

# 计算相对位置
def get_relative_position_df(stock_list, date, watch_days):
    rp_list = []
    for stock in stock_list:
        try:
            # 确保日期是字符串格式
            if hasattr(date, 'strftime'):
                date_str = date.strftime('%Y-%m-%d')
            else:
                date_str = str(date)

            df = get_price(stock, end_date=date_str, frequency='1d', fields=['close','high','low'], count=watch_days)
            if not df.empty and len(df) >= watch_days:
                close_price = df['close'].iloc[-1]
                high_price = df['high'].max()
                low_price = df['low'].min()
                if high_price != low_price:
                    rp = (close_price - low_price) / (high_price - low_price)
                else:
                    rp = 0.5
                rp_list.append(rp)
            else:
                rp_list.append(0.5)
        except:
            rp_list.append(0.5)
    df = pd.DataFrame(index=stock_list, data={'rp':rp_list})
    return df

# 每日初始股票池2
def prepare_stock_list2(date):
    # 确保date是字符串格式，get_Ashares需要YYYYmmdd格式
    if hasattr(date, 'strftime'):
        date_str = date.strftime('%Y%m%d')
    else:
        # 如果是字符串，尝试转换格式
        if isinstance(date, str) and '-' in date:
            date_str = date.replace('-', '')
        else:
            date_str = str(date)

    initial_list = get_Ashares(date_str)
    initial_list = filter_kcbj_stock(initial_list)
    # 过滤函数需要原始的date对象，不是date_str
    initial_list = filter_new_stock(initial_list, date)
    initial_list = filter_st_stock(initial_list, date)
    initial_list = filter_paused_stock(initial_list, date)
    return initial_list

# 卖出
def sell(context):
    # 获取当前持仓
    positions = context.portfolio.positions
    if not positions:
        return

    for stock in list(positions.keys()):
        position = positions[stock]
        if position.amount <= 0:
            continue

        # 获取5日均线
        try:
            df = get_history(5, '1d', ['close'], stock, include=False)
            if len(df) < 5:
                continue
            ma5 = df['close'].mean()

            # 获取当前价格
            snapshot = get_snapshot(stock)
            current_price = snapshot[stock]['last_px']

            # 如果跌破5日均线，卖出
            if current_price < ma5:
                order_target(stock, 0)
                log.info(f'卖出{stock}，原因：跌破5日均线')
        except Exception as e:
            log.info(f'处理{stock}时出错: {str(e)}')
            continue

def handle_data(context, data):
    pass
