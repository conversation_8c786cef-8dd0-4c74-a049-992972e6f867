# Ptrade策略调试日志说明

## 调试日志添加概述

为了找出策略"每天都是无目标个股"的问题，我已经为策略添加了详细的调试日志。

## 添加的调试日志位置

### 1. 选股函数 (get_stock_list)
- **初始股票池获取**：
  - 记录选股日期
  - 记录初始股票池数量
  - 记录各个过滤步骤的结果

- **昨日涨停股票获取**：
  - 记录昨日涨停股票数量
  - 记录前日曾涨停股票数量
  - 记录前前日曾涨停股票数量
  - 记录过滤后的target_list数量

- **弱转强候选股票**：
  - 记录昨日曾涨停股票数量
  - 记录过滤后的target_list2数量

### 2. 买入函数 (buy) - 三大策略
- **一进二策略**：
  - 记录候选股票数量
  - 如果无候选股票，记录提示信息

- **首板低开策略**：
  - 记录策略开始执行
  - 记录初始股票池数量
  - 记录昨日涨停股票数量

- **弱转强策略**：
  - 记录候选股票数量
  - 如果无候选股票，记录提示信息

### 3. 选股结果汇总
- **详细统计**：
  - 各策略选出的股票数量
  - 总计选出的股票数量
  - 如果无目标个股，列出可能原因

## 关键调试信息

### 问题诊断流程
1. **检查初始股票池**：是否为空
2. **检查涨停股票获取**：各日期的涨停股票数量
3. **检查过滤结果**：target_list和target_list2的数量
4. **检查策略执行**：每个策略的候选股票数量
5. **检查最终结果**：各策略选出的股票数量

### 预期的日志输出示例
```
2025-05-16 09:31:00 - INFO - 选股日期: 2025-05-15, 2025-05-14, 2025-05-13
2025-05-16 09:31:00 - INFO - 开始获取初始股票池...
2025-05-16 09:31:00 - INFO - 初始股票池数量: 2965
2025-05-16 09:31:00 - INFO - 获取昨日涨停股票...
2025-05-16 09:31:00 - INFO - 昨日涨停股票数量: 50
2025-05-16 09:31:00 - INFO - 过滤后的昨日涨停股票数量(target_list): 30
2025-05-16 09:31:00 - INFO - 过滤后的弱转强候选股票数量(target_list2): 25
2025-05-16 09:31:00 - INFO - 开始执行一进二策略，候选股票数量: 30
2025-05-16 09:31:00 - INFO - 开始执行首板低开策略...
2025-05-16 09:31:00 - INFO - 开始执行弱转强策略，候选股票数量: 25
```

## 问题排查指南

### 如果初始股票池为空
- 检查`get_Ashares`函数是否正常工作
- 检查日期格式是否正确

### 如果涨停股票数量为0
- 检查`get_hl_stock`函数
- 检查价格数据获取是否正常

### 如果候选股票池不为空但最终无选股
- 检查各个过滤条件是否过于严格
- 检查集合竞价数据获取是否正常
- 检查基本面数据获取是否正常

## 使用方法

1. **运行策略**：在Ptrade平台运行策略
2. **查看日志**：观察详细的调试日志输出
3. **定位问题**：根据日志信息定位问题所在环节
4. **针对性修复**：根据问题环节进行针对性修复

## 注意事项

- 这些调试日志会增加策略的日志输出量
- 在问题解决后，可以考虑移除部分详细日志以提高性能
- 日志中的"未定义"警告是IDE提示，在Ptrade环境中这些函数是可用的
